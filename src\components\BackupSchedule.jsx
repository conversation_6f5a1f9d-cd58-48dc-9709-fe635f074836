import React from 'react';

function BackupSchedule({ medications }) {
  // EST timezone offset (UTC-5, or UTC-4 during daylight saving time)
  // For simplicity, using EST (UTC-5)
  const EST_OFFSET = -5;
  
  const getESTDate = (date) => {
    const utc = date.getTime() + (date.getTimezoneOffset() * 60000);
    return new Date(utc + (EST_OFFSET * 3600000));
  };

  const generateSchedule = () => {
    const schedule = [];
    const startDate = new Date(); // Today
    
    // Generate 14 days of schedule
    for (let day = 0; day < 14; day++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + day);
      const estDate = getESTDate(currentDate);
      
      const daySchedule = {
        date: estDate.toLocaleDateString('en-US', { 
          weekday: 'long', 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        }),
        medications: []
      };

      medications.forEach(med => {
        const medSchedule = generateMedicationSchedule(med, day);
        if (medSchedule.length > 0) {
          daySchedule.medications.push({
            name: med.name,
            dose: med.dose,
            times: medSchedule,
            instructions: med.instructions,
            type: med.type
          });
        }
      });

      schedule.push(daySchedule);
    }
    
    return schedule;
  };

  const generateMedicationSchedule = (medication, dayNumber) => {
    const times = [];
    const freq = medication.frequency.toLowerCase();
    
    if (freq.includes('once daily') || freq.includes('once a day')) {
      // Take once daily - morning
      times.push('8:00 AM');
    } else if (freq.includes('twice daily') || freq.includes('every 12 hours')) {
      // Take twice daily
      times.push('8:00 AM', '8:00 PM');
    } else if (freq.includes('every 6 hours')) {
      // Take every 6 hours
      times.push('6:00 AM', '12:00 PM', '6:00 PM', '12:00 AM');
    } else if (freq.includes('every 8 hours')) {
      // Take every 8 hours
      times.push('8:00 AM', '4:00 PM', '12:00 AM');
    } else if (freq.includes('as needed') || freq.includes('when required')) {
      // As needed medications - show reminder
      times.push('As needed');
    } else if (freq.includes('for 7 days')) {
      // Only for first 7 days
      if (dayNumber < 7) {
        if (freq.includes('every 6 hours')) {
          times.push('6:00 AM', '12:00 PM', '6:00 PM', '12:00 AM');
        } else {
          times.push('8:00 AM');
        }
      }
    }
    
    return times;
  };

  const schedule = generateSchedule();

  return (
    <div className="backup-schedule">
      <div className="schedule-header">
        <h2>📅 14-Day Medication Schedule (EST)</h2>
        <p className="schedule-subtitle">
          Backup reference for when the web app isn't available
        </p>
      </div>

      <div className="schedule-content">
        {schedule.map((day, index) => (
          <div key={index} className="day-schedule">
            <h3 className="day-header">{day.date}</h3>
            
            {day.medications.length === 0 ? (
              <p className="no-meds">No scheduled medications for this day</p>
            ) : (
              <div className="day-medications">
                {day.medications.map((med, medIndex) => (
                  <div key={medIndex} className={`med-schedule ${med.type.toLowerCase()}`}>
                    <div className="med-header">
                      <h4 className="med-name">{med.name}</h4>
                      <span className="med-type">{med.type}</span>
                    </div>
                    
                    <div className="med-details">
                      <p className="med-dose"><strong>Dose:</strong> {med.dose}</p>
                      <p className="med-instructions"><strong>Instructions:</strong> {med.instructions}</p>
                      
                      <div className="med-times">
                        <strong>Times to take:</strong>
                        <ul className="time-list">
                          {med.times.map((time, timeIndex) => (
                            <li key={timeIndex} className="time-item">
                              <span className="time">{time}</span>
                              <span className="checkbox">☐</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="schedule-footer">
        <div className="important-notes">
          <h3>⚠️ Important Notes:</h3>
          <ul>
            <li><strong>Clindamycin & Cephalexin:</strong> Take for 7 days only (first week)</li>
            <li><strong>With Food:</strong> Take Lansoprazole and antibiotics with food when possible</li>
            <li><strong>As Needed:</strong> Tylenol, Senna, and Morphine only when symptoms require</li>
            <li><strong>Spacing:</strong> Try to space medications evenly throughout the day</li>
            <li><strong>Questions:</strong> Contact your doctor if you have any concerns</li>
          </ul>
        </div>
        
        <div className="print-note">
          <p><strong>💡 Tip:</strong> Print this page for offline reference</p>
        </div>
      </div>
    </div>
  );
}

export default BackupSchedule;
