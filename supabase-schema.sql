-- Supabase Database Schema for Medication Tracker
-- Run this SQL in your Supabase SQL editor to set up the database

-- Create medications table
CREATE TABLE IF NOT EXISTS medications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  medication_id INTEGER NOT NULL, -- Original medication ID from the app
  name TEXT NOT NULL,
  dose TEXT NOT NULL,
  frequency TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('Required', 'Optional')),
  instructions TEXT NOT NULL,
  last_taken TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure one medication per user per medication_id
  UNIQUE(user_id, medication_id)
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_medications_user_id ON medications(user_id);
CREATE INDEX IF NOT EXISTS idx_medications_user_medication ON medications(user_id, medication_id);

-- Enable Row Level Security (RLS)
ALTER TABLE medications ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to only access their own medications
CREATE POLICY "Users can only access their own medications" ON medications
  FOR ALL USING (auth.uid() = user_id);

-- Create policy for anonymous users (optional, for demo purposes)
-- This allows anonymous users to access medications they created
CREATE POLICY "Anonymous users can access their medications" ON medications
  FOR ALL USING (
    auth.uid() = user_id OR 
    (auth.uid() IS NULL AND user_id IS NULL)
  );

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_medications_updated_at 
  BEFORE UPDATE ON medications 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Optional: Create a view for easier querying
CREATE OR REPLACE VIEW user_medications AS
SELECT 
  id,
  user_id,
  medication_id,
  name,
  dose,
  frequency,
  type,
  instructions,
  last_taken,
  created_at,
  updated_at,
  CASE 
    WHEN last_taken IS NULL THEN 'Not taken'
    ELSE last_taken::text
  END as last_taken_display
FROM medications
WHERE user_id = auth.uid();

-- Grant permissions
GRANT ALL ON medications TO authenticated;
GRANT ALL ON medications TO anon;
GRANT SELECT ON user_medications TO authenticated;
GRANT SELECT ON user_medications TO anon;
