import { createClient } from '@supabase/supabase-js';

// Supabase configuration
// These will be environment variables in production
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

// Create Supabase client
export const supabase = supabaseUrl && supabaseAnonKey
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null;

// Check if Supabase is configured
export const isSupabaseConfigured = () => {
  return supabase !== null;
};

// Test database connection
export const testConnection = async () => {
  if (!supabase) {
    return { success: false, error: 'Supabase not configured' };
  }

  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('medications')
      .select('count', { count: 'exact', head: true });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, message: 'Database connection successful' };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

// Database operations
export const medicationService = {
  // Get all medications for a user
  async getMedications(userId) {
    if (!supabase) throw new Error('Supabase not configured');
    
    const { data, error } = await supabase
      .from('medications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: true });
    
    if (error) throw error;
    
    // Convert lastTaken strings back to Date objects and map field names
    return data.map(med => ({
      id: med.medication_id,
      name: med.name,
      dose: med.dose,
      frequency: med.frequency,
      type: med.type,
      instructions: med.instructions,
      lastTaken: med.last_taken ? new Date(med.last_taken) : null,
      timesUsed: med.times_used || 0
    }));
  },

  // Update medication last taken time
  async updateMedicationTaken(userId, medicationId, lastTaken, timesUsed) {
    if (!supabase) throw new Error('Supabase not configured');

    const updateData = {
      last_taken: lastTaken ? lastTaken.toISOString() : null,
      updated_at: new Date().toISOString()
    };

    if (timesUsed !== undefined) {
      updateData.times_used = timesUsed;
    }

    const { data, error } = await supabase
      .from('medications')
      .update(updateData)
      .eq('user_id', userId)
      .eq('medication_id', medicationId)
      .select();

    if (error) throw error;
    return data[0];
  },

  // Initialize medications for a new user
  async initializeMedications(userId, medications) {
    if (!supabase) throw new Error('Supabase not configured');
    
    const medicationsToInsert = medications.map(med => ({
      user_id: userId,
      medication_id: med.id,
      name: med.name,
      dose: med.dose,
      frequency: med.frequency,
      type: med.type,
      instructions: med.instructions,
      last_taken: med.lastTaken ? med.lastTaken.toISOString() : null,
      times_used: med.timesUsed || 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    const { data, error } = await supabase
      .from('medications')
      .insert(medicationsToInsert)
      .select();
    
    if (error) throw error;
    return data;
  },

  // Sync local medications with database
  async syncMedications(userId, localMedications) {
    if (!supabase) throw new Error('Supabase not configured');
    
    try {
      // First, try to get existing medications
      const existingMeds = await this.getMedications(userId);
      
      if (existingMeds.length === 0) {
        // No existing medications, initialize with local data
        return await this.initializeMedications(userId, localMedications);
      } else {
        // Merge local changes with database
        const updates = [];
        
        for (const localMed of localMedications) {
          const existingMed = existingMeds.find(med => med.id === localMed.id);

          if (existingMed && localMed.lastTaken &&
              (!existingMed.lastTaken || localMed.lastTaken > existingMed.lastTaken)) {
            // Local medication has newer lastTaken time
            updates.push(this.updateMedicationTaken(userId, existingMed.id, localMed.lastTaken, localMed.timesUsed));
          }
        }
        
        await Promise.all(updates);
        return await this.getMedications(userId);
      }
    } catch (error) {
      console.error('Error syncing medications:', error);
      throw error;
    }
  }
};

// User authentication helpers
export const authService = {
  // Get current user
  async getCurrentUser() {
    if (!supabase) return null;
    
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  },

  // Sign in anonymously (for demo purposes)
  async signInAnonymously() {
    if (!supabase) throw new Error('Supabase not configured');
    
    const { data, error } = await supabase.auth.signInAnonymously();
    if (error) throw error;
    return data.user;
  },

  // Sign out
  async signOut() {
    if (!supabase) return;
    
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  // Listen to auth changes
  onAuthStateChange(callback) {
    if (!supabase) return () => {};
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange(callback);
    return () => subscription.unsubscribe();
  }
};
