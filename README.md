# 💊 Medication Tracker - Post-Surgery Recovery

A React-based medication tracking application designed to help manage post-surgery medications with real-time due times, dosage tracking, and schedule management.

## Features

- **📋 Medication Management**: Track all medications with detailed information
- **⏰ Real-Time Due Times**: Automatic calculation of next due times
- **🔴 Required vs Optional**: Clear distinction between required and as-needed medications
- **📱 Responsive Design**: Works seamlessly on mobile and desktop
- **💾 Hybrid Data Storage**: Local storage with optional cloud sync via Supabase
- **📥 Multiple Export Options**: Download as CSV or PDF, plus print functionality
- **☁️ Cloud Sync**: Optional Supabase integration for cross-device synchronization
- **🔄 Offline Support**: Works offline with automatic sync when online
- **🎨 Modern UI**: Clean, intuitive interface with status indicators

## Medications Included

### Required Medications
- **Lansoprazole** (30 mg) - Once daily
- **Clindamycin** (300 mg) - Every 6 hours for 7 days
- **Cephalexin** (10 mL) - Every 6 hours for 7 days

### Optional Medications (As Needed)
- **Acetaminophen (Tylenol)** (2 tablets/1000 mg) - Every 6 hours as needed
- **<PERSON>na** (2 tablets) - Once daily when required
- **Morphine** (5 mg) - As needed for severe pain

## Getting Started

### Prerequisites
- Node.js 18 or higher
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd becky-recovery-app
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## Cloud Sync Setup (Optional)

The app works perfectly with local storage only, but you can optionally set up cloud synchronization using Supabase for free.

### Setting up Supabase (Free Tier)

1. **Create a Supabase Account**:
   - Go to [supabase.com](https://supabase.com)
   - Sign up for a free account
   - Create a new project

2. **Set up the Database**:
   - In your Supabase dashboard, go to the SQL Editor
   - Copy and paste the contents of `supabase-schema.sql`
   - Run the SQL to create the necessary tables and policies

3. **Get Your Project Credentials**:
   - Go to Settings > API in your Supabase dashboard
   - Copy your Project URL and anon public key

4. **Configure Environment Variables**:
   - Copy `.env.example` to `.env`
   - Fill in your Supabase credentials:
   ```bash
   VITE_SUPABASE_URL=your_supabase_project_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

5. **Deploy with Environment Variables**:
   - For Netlify: Add environment variables in Site Settings > Environment Variables
   - For other platforms: Follow their documentation for environment variables

### How Cloud Sync Works

- **Automatic**: The app automatically syncs when online
- **Offline-First**: Works completely offline, syncs when connection returns
- **Anonymous Users**: Uses anonymous authentication for privacy
- **Conflict Resolution**: Newer timestamps take precedence during sync
- **Fallback**: If cloud sync fails, app continues with local storage

### Data Storage Options

1. **Local Only** (Default): Data stored in browser's localStorage
2. **Cloud Sync** (Optional): Data synced to Supabase with local fallback
3. **Hybrid**: Best of both - works offline, syncs when online

## Deployment

### Netlify Deployment

1. **Connect to GitHub**: Push your code to a GitHub repository
2. **Deploy to Netlify**: 
   - Go to [Netlify](https://netlify.com)
   - Click "New site from Git"
   - Connect your GitHub repository
   - Build settings are automatically configured via `netlify.toml`
3. **Custom Domain** (optional): Configure a custom domain in Netlify settings

### Manual Deployment

1. Build the project: `npm run build`
2. Upload the `dist` folder to your web hosting service

## Usage

### Marking Medications as Taken
- Click the "✅ Take Now" button to mark a medication as taken
- The "Last Taken" and "Next Due" times will update automatically

### Resetting Medications
- Click the "🔄 Reset" button to reset a medication to "not taken"

### Exporting Data
- **📊 Download CSV**: Export medication data as a spreadsheet
- **📄 Download PDF**: Generate a formatted PDF report
- **🖨️ Print PDF**: Print the medication schedule directly

### Status Indicators
- **Red border**: Required medication that's overdue or not taken
- **Orange border**: Optional medication that's overdue
- **Green border**: Medication taken on time
- **Gray border**: Optional medication not yet taken

## Technical Details

### Built With
- **React 18** - Frontend framework
- **Vite** - Build tool and development server
- **Vanilla CSS** - Styling with modern CSS features
- **Local Storage API** - Data persistence

### Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit your changes: `git commit -am 'Add feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

If you encounter any issues or have questions, please create an issue in the GitHub repository.
