import React, { useState, useEffect } from 'react';
import MedicationList from './components/MedicationList';
import BackupSchedule from './components/BackupSchedule';
import { calculateNextDueTime } from './utils/dueTimeCalculator';
import { downloadPDF } from './utils/pdfExport';
import {
  initializeDataSync,
  updateMedicationWithSync,
  getSyncStatus,
  dataSyncService
} from './utils/dataSync';
import { testConnection } from './utils/supabase';
import './styles/App.css';

function App() {
  const [medications, setMedications] = useState([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [currentView, setCurrentView] = useState('tracker'); // 'tracker' or 'schedule'
  const [syncStatus, setSyncStatus] = useState({
    isOnline: navigator.onLine,
    hasCloudSync: false,
    syncInProgress: false,
    user: null
  });

  // Initialize data sync on component mount
  useEffect(() => {
    const initializeApp = async () => {
      try {
        const initialMedications = await initializeDataSync();
        setMedications(initialMedications);
        setSyncStatus(getSyncStatus());
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, []);

  // Update sync status periodically
  useEffect(() => {
    const updateSyncStatus = () => {
      setSyncStatus(getSyncStatus());
    };

    const interval = setInterval(updateSyncStatus, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, []);

  // Update current time every minute to refresh due times
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  const handleMarkTaken = async (id) => {
    try {
      const medication = medications.find(med => med.id === id);
      const updatedMedications = await updateMedicationWithSync(
        medications,
        id,
        {
          lastTaken: new Date(),
          timesUsed: (medication.timesUsed || 0) + 1
        }
      );
      setMedications(updatedMedications);
      setSyncStatus(getSyncStatus());
    } catch (error) {
      console.error('Failed to mark medication as taken:', error);
    }
  };

  const handleResetMedication = async (id) => {
    try {
      const updatedMedications = await updateMedicationWithSync(
        medications,
        id,
        { lastTaken: null, timesUsed: 0 }
      );
      setMedications(updatedMedications);
      setSyncStatus(getSyncStatus());
    } catch (error) {
      console.error('Failed to reset medication:', error);
    }
  };

  const handleDownloadPDF = () => {
    downloadPDF(medications);
  };

  const handleForceSync = async () => {
    try {
      const syncedMedications = await dataSyncService.forceSync();
      setMedications(syncedMedications);
      setSyncStatus(getSyncStatus());
    } catch (error) {
      console.error('Failed to force sync:', error);
    }
  };

  const handleTestConnection = async () => {
    try {
      const result = await testConnection();
      if (result.success) {
        alert('✅ Database connection successful!');
      } else {
        alert(`❌ Connection failed: ${result.error}`);
      }
    } catch (error) {
      alert(`❌ Connection test failed: ${error.message}`);
    }
  };

  // Separate required and optional medications
  const requiredMedications = medications.filter(med => med.type === 'Required');
  const optionalMedications = medications.filter(med => med.type === 'Optional');

  return (
    <div className="app">
      <header className="app-header">
        <h1>Medication Tracker</h1>
        <p className="subtitle">Post-Surgery Recovery Management</p>
        <div className="header-actions">
          <div className="nav-buttons">
            <button
              onClick={() => setCurrentView('tracker')}
              className={`nav-btn ${currentView === 'tracker' ? 'active' : ''}`}
            >
              Tracker
            </button>
            <button
              onClick={() => setCurrentView('schedule')}
              className={`nav-btn ${currentView === 'schedule' ? 'active' : ''}`}
            >
              Schedule
            </button>
          </div>

          {currentView === 'tracker' && (
            <div className="export-buttons">
              <button onClick={handleDownloadPDF} className="download-btn pdf">
                Download PDF
              </button>
              <button onClick={handleTestConnection} className="download-btn test">
                Test DB Connection
              </button>
            </div>
          )}
          <div className="sync-status">
            <div className="current-time">
              Last updated: {currentTime.toLocaleTimeString()}
            </div>
            <div className={`sync-indicator ${syncStatus.hasCloudSync ? 'cloud' : 'local'}`}>
              {syncStatus.syncInProgress ? (
                <span>Syncing...</span>
              ) : syncStatus.hasCloudSync ? (
                <span>Cloud sync {syncStatus.isOnline ? 'enabled' : 'offline'}</span>
              ) : (
                <span>Local storage only</span>
              )}
              {syncStatus.hasCloudSync && syncStatus.isOnline && (
                <button onClick={handleForceSync} className="sync-btn" title="Force sync with cloud">
                  ↻
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      <main className="app-main">
        {currentView === 'tracker' ? (
          <>
            <section className="medication-section">
              <h2 className="section-title required">Required Medications</h2>
              <MedicationList
                medications={requiredMedications}
                handleMarkTaken={handleMarkTaken}
                handleResetMedication={handleResetMedication}
                calculateNextDueTime={calculateNextDueTime}
                currentTime={currentTime}
              />
            </section>

            <section className="medication-section">
              <h2 className="section-title optional">Optional Medications (As Needed)</h2>
              <MedicationList
                medications={optionalMedications}
                handleMarkTaken={handleMarkTaken}
                handleResetMedication={handleResetMedication}
                calculateNextDueTime={calculateNextDueTime}
                currentTime={currentTime}
              />
            </section>
          </>
        ) : (
          <BackupSchedule medications={medications} />
        )}
      </main>
    </div>
  );
}

export default App;
