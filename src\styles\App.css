/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f1f5f9;
  min-height: 100vh;
  color: #0f172a;
  font-size: 16px;
}

/* App container */
.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background: white;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  text-align: center;
}

.app-header h1 {
  font-size: 2.25rem;
  color: #1e293b;
  margin-bottom: 8px;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.subtitle {
  color: #64748b;
  font-size: 1rem;
  margin-bottom: 24px;
  font-weight: 400;
}

.header-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.nav-buttons {
  display: flex;
  gap: 4px;
  background: #f1f5f9;
  padding: 4px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.nav-btn {
  background: transparent;
  color: #64748b;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.nav-btn.active {
  background: white;
  color: #0f172a;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.export-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.download-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.download-btn:hover {
  background: #2563eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.download-btn.test {
  background: #10b981;
}

.download-btn.test:hover {
  background: #059669;
}

.download-btn.print {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
  box-shadow: 0 4px 12px rgba(237, 137, 54, 0.3);
}

.download-btn.print:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(237, 137, 54, 0.4);
}

.sync-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.current-time {
  color: #718096;
  font-size: 0.9rem;
}

.sync-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.75rem;
  padding: 4px 8px;
  border-radius: 6px;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
}

.sync-indicator.cloud {
  color: #3b82f6;
  background: #eff6ff;
  border-color: #bfdbfe;
}

.sync-indicator.local {
  color: #64748b;
  background: #f8fafc;
  border-color: #e2e8f0;
}

.sync-btn {
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  font-size: 0.75rem;
  padding: 4px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: #64748b;
}

.sync-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

/* Main content */
.app-main {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Medication sections */
.medication-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f1f5f9;
  color: #0f172a;
}

.section-title.required {
  border-bottom-color: #dc2626;
}

.section-title.optional {
  border-bottom-color: #f59e0b;
}

/* Medication table */
.medication-table {
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: transparent;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1fr 1.2fr 1.5fr 2fr 1.2fr;
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  font-size: 0.875rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px 8px 0 0;
}

.header-cell {
  padding: 12px 16px;
  text-align: left;
}

/* Medication rows */
.medication-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1fr 1.2fr 1.5fr 2fr 1.2fr;
  background: white;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
  border-top: none;
}

.medication-row:hover {
  background: #f9fafb;
}

.medication-row:last-child {
  border-radius: 0 0 8px 8px;
}

/* Status-based styling */
.status-not-taken {
  background: #f8fafc;
}

.status-not-taken-required {
  background: #fef2f2;
  border-left: 3px solid #fca5a5;
}

.status-overdue {
  background: #fffbeb;
  border-left: 3px solid #fbbf24;
}

.status-overdue-required {
  background: #fef2f2;
  border-left: 3px solid #ef4444;
}

.status-on-time {
  background: #f0fdf4;
  border-left: 3px solid #22c55e;
  background: #f0fff4;
}

.cell {
  padding: 16px 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 80px;
}

/* Cell content styling */
.medication-name {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
}

.medication-type {
  font-size: 0.8rem;
  color: #718096;
  font-weight: 500;
}

.dose-value {
  font-weight: 600;
  color: #4a5568;
}

.frequency-value {
  font-size: 0.9rem;
  color: #4a5568;
  line-height: 1.4;
}

.times-used-value {
  font-weight: 600;
  color: #2d3748;
  background: #edf2f7;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.85rem;
  display: inline-block;
}

.last-taken-value {
  font-size: 0.9rem;
  color: #4a5568;
}

.next-due-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2d3748;
}

.next-due-value.overdue {
  color: #e53e3e;
  font-weight: 700;
}

.instructions-text {
  font-size: 0.9rem;
  color: #4a5568;
  line-height: 1.4;
}

/* Action buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mark-btn, .reset-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.mark-btn {
  background: #10b981;
  color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.mark-btn:hover {
  background: #059669;
}

.reset-btn {
  background: #6b7280;
  color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.reset-btn:hover {
  background: #4b5563;
}

/* Footer */
.app-footer {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 20px;
  margin-top: 30px;
  text-align: center;
  color: #4a5568;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

/* No medications message */
.no-medications {
  text-align: center;
  padding: 40px;
  color: #718096;
  font-style: italic;
}

/* Backup Schedule Styles */
.backup-schedule {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.schedule-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.schedule-header h2 {
  margin: 0 0 10px 0;
  font-size: 2rem;
}

.schedule-subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

.day-schedule {
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.day-header {
  background: #4a5568;
  color: white;
  margin: 0;
  padding: 15px 20px;
  font-size: 1.3rem;
  font-weight: 600;
}

.no-meds {
  padding: 20px;
  text-align: center;
  color: #718096;
  font-style: italic;
}

.day-medications {
  padding: 20px;
  display: grid;
  gap: 15px;
}

.med-schedule {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 15px;
  background: #f7fafc;
}

.med-schedule.required {
  border-color: #e53e3e;
  background: #fed7d7;
}

.med-schedule.optional {
  border-color: #d69e2e;
  background: #faf089;
}

.med-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.med-name {
  margin: 0;
  color: #2d3748;
  font-size: 1.1rem;
}

.med-type {
  background: #4a5568;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.med-details p {
  margin: 8px 0;
  color: #4a5568;
}

.med-times {
  margin-top: 12px;
}

.time-list {
  list-style: none;
  padding: 0;
  margin: 8px 0 0 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.time {
  font-weight: 600;
  color: #2d3748;
}

.checkbox {
  font-size: 1.2rem;
  color: #4a5568;
}

.schedule-footer {
  margin-top: 40px;
  padding: 20px;
  background: #edf2f7;
  border-radius: 12px;
}

.important-notes h3 {
  color: #e53e3e;
  margin-bottom: 15px;
}

.important-notes ul {
  color: #4a5568;
  line-height: 1.6;
}

.important-notes li {
  margin-bottom: 8px;
}

.print-note {
  margin-top: 20px;
  text-align: center;
  padding: 15px;
  background: #bee3f8;
  border-radius: 8px;
  color: #2c5282;
}

/* Responsive design */
@media (max-width: 1200px) {
  .table-header,
  .medication-row {
    grid-template-columns: 1.5fr 0.8fr 1.2fr 1fr 1.2fr 1.5fr 1fr;
  }
}

@media (max-width: 768px) {
  .app {
    padding: 10px;
  }

  .app-header {
    padding: 20px;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 15px;
  }

  .medication-section {
    padding: 15px;
  }

  .section-title {
    font-size: 1.3rem;
  }

  /* Mobile card layout */
  .medication-table {
    display: block;
    background: transparent;
  }

  .table-header {
    display: none;
  }

  .medication-row {
    display: block;
    background: white;
    border-radius: 12px;
    margin-bottom: 16px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
  }

  .cell {
    display: block;
    padding: 8px 0;
    min-height: auto;
    border-bottom: 1px solid #f1f5f9;
  }

  .cell:last-child {
    border-bottom: none;
  }

  .cell .medication-name {
    font-size: 1.1rem;
    margin-bottom: 8px;
  }

  .cell .medication-type {
    font-size: 0.9rem;
    margin-bottom: 12px;
  }

  .cell::before {
    content: attr(data-label);
    font-weight: 600;
    color: #4a5568;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
    display: block;
  }

  .cell.name::before {
    display: none;
  }

  .action-buttons {
    flex-direction: row;
    justify-content: center;
    gap: 12px;
    margin-top: 12px;
  }

  .mark-btn, .reset-btn {
    flex: 1;
    max-width: 120px;
  }
}
