import jsPDF from 'jspdf';

export function generatePDF(medications) {
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  const margin = 20;
  let yPosition = margin;

  // Header
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text('Medication Schedule', pageWidth / 2, yPosition, { align: 'center' });

  yPosition += 10;
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text('Post-Surgery Recovery Management', pageWidth / 2, yPosition, { align: 'center' });

  yPosition += 15;
  doc.setFontSize(10);
  doc.text(`Generated on: ${new Date().toLocaleString()}`, pageWidth / 2, yPosition, { align: 'center' });
  
  yPosition += 20;

  // Separate medications by type
  const requiredMeds = medications.filter(med => med.type === 'Required');
  const optionalMeds = medications.filter(med => med.type === 'Optional');

  // Required Medications Section
  if (requiredMeds.length > 0) {
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(220, 53, 69); // Red color
    doc.text('REQUIRED MEDICATIONS', margin, yPosition);
    yPosition += 15;

    requiredMeds.forEach((med) => {
      yPosition = addMedicationToPDF(doc, med, yPosition, margin, pageWidth, pageHeight);
    });

    yPosition += 10;
  }

  // Optional Medications Section
  if (optionalMeds.length > 0) {
    // Check if we need a new page
    if (yPosition > pageHeight - 60) {
      doc.addPage();
      yPosition = margin;
    }

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(214, 158, 46); // Orange color
    doc.text('OPTIONAL MEDICATIONS (As Needed)', margin, yPosition);
    yPosition += 15;

    optionalMeds.forEach((med) => {
      yPosition = addMedicationToPDF(doc, med, yPosition, margin, pageWidth, pageHeight);
    });
  }

  // Footer
  const footerY = pageHeight - 20;
  doc.setFontSize(8);
  doc.setTextColor(100, 100, 100);
  doc.setFont('helvetica', 'italic');
  doc.text('WARNING: Always consult with your healthcare provider before making changes to your medication schedule.',
           pageWidth / 2, footerY, { align: 'center' });

  return doc;
}

function addMedicationToPDF(doc, medication, yPosition, margin, pageWidth, pageHeight) {
  // Check if we need a new page
  if (yPosition > pageHeight - 80) {
    doc.addPage();
    yPosition = margin;
  }

  const boxHeight = 45;
  const boxWidth = pageWidth - (margin * 2);

  // Draw medication box
  doc.setDrawColor(200, 200, 200);
  doc.setFillColor(248, 249, 250);
  doc.rect(margin, yPosition, boxWidth, boxHeight, 'FD');

  // Medication name
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.text(medication.name, margin + 5, yPosition + 8);

  // Dose and frequency
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text(`Dose: ${medication.dose}`, margin + 5, yPosition + 18);
  doc.text(`Frequency: ${medication.frequency}`, margin + 5, yPosition + 28);

  // Last taken
  const lastTakenText = medication.lastTaken
    ? `Last taken: ${medication.lastTaken.toLocaleString()}`
    : 'Last taken: Not taken yet';
  doc.text(lastTakenText, margin + 5, yPosition + 38);

  // Instructions (right side)
  const instructionsX = pageWidth / 2 + 10;
  doc.setFont('helvetica', 'italic');
  doc.text('Instructions:', instructionsX, yPosition + 8);
  
  // Split long instructions into multiple lines
  const maxWidth = (pageWidth / 2) - 20;
  const instructionLines = doc.splitTextToSize(medication.instructions, maxWidth);
  
  let instructionY = yPosition + 18;
  instructionLines.forEach((line) => {
    if (instructionY < yPosition + boxHeight - 5) {
      doc.text(line, instructionsX, instructionY);
      instructionY += 8;
    }
  });

  return yPosition + boxHeight + 10;
}

export function downloadPDF(medications, filename = 'medication_schedule.pdf') {
  const doc = generatePDF(medications);
  doc.save(filename);
}

export function printPDF(medications) {
  const doc = generatePDF(medications);
  doc.autoPrint();
  window.open(doc.output('bloburl'), '_blank');
}
