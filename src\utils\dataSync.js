import { medicationService, authService, isSupabaseConfigured } from './supabase';
import { 
  loadMedicationsFromStorage, 
  saveMedicationsToStorage, 
  initialMedications 
} from './medicationData';

// Enhanced data persistence with cloud sync
export class DataSyncService {
  constructor() {
    this.isOnline = navigator.onLine;
    this.syncInProgress = false;
    this.user = null;
    
    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.syncWithCloud();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  // Initialize the service
  async initialize() {
    if (!isSupabaseConfigured()) {
      console.log('Supabase not configured, using local storage only');
      return this.loadFromLocalStorage();
    }

    try {
      // Try to get current user or sign in anonymously
      this.user = await authService.getCurrentUser();
      
      if (!this.user && this.isOnline) {
        this.user = await authService.signInAnonymously();
      }

      if (this.user && this.isOnline) {
        return await this.syncWithCloud();
      } else {
        return this.loadFromLocalStorage();
      }
    } catch (error) {
      console.error('Failed to initialize cloud sync:', error);
      return this.loadFromLocalStorage();
    }
  }

  // Load medications from local storage
  loadFromLocalStorage() {
    const medications = loadMedicationsFromStorage();
    return medications.length > 0 ? medications : initialMedications;
  }

  // Save medications to local storage
  saveToLocalStorage(medications) {
    saveMedicationsToStorage(medications);
  }

  // Sync with cloud database
  async syncWithCloud() {
    if (!isSupabaseConfigured() || !this.user || this.syncInProgress) {
      return this.loadFromLocalStorage();
    }

    this.syncInProgress = true;

    try {
      const localMedications = this.loadFromLocalStorage();
      
      // Sync with cloud
      const cloudMedications = await medicationService.syncMedications(
        this.user.id, 
        localMedications
      );

      // Update local storage with synced data
      this.saveToLocalStorage(cloudMedications);
      
      console.log('Successfully synced with cloud');
      return cloudMedications;
    } catch (error) {
      console.error('Cloud sync failed, using local data:', error);
      return this.loadFromLocalStorage();
    } finally {
      this.syncInProgress = false;
    }
  }

  // Update medication and sync
  async updateMedication(medications, medicationId, updates) {
    // Update local data first
    const updatedMedications = medications.map(med =>
      med.id === medicationId ? { ...med, ...updates } : med
    );

    // Save to local storage immediately
    this.saveToLocalStorage(updatedMedications);

    // Try to sync with cloud if online
    if (isSupabaseConfigured() && this.user && this.isOnline) {
      try {
        if (updates.lastTaken !== undefined) {
          await medicationService.updateMedicationTaken(
            this.user.id,
            medicationId,
            updates.lastTaken,
            updates.timesUsed
          );
        }
      } catch (error) {
        console.error('Failed to sync medication update to cloud:', error);
        // Local update is already saved, so we continue
      }
    }

    return updatedMedications;
  }

  // Get sync status
  getSyncStatus() {
    return {
      isOnline: this.isOnline,
      hasCloudSync: isSupabaseConfigured() && this.user !== null,
      syncInProgress: this.syncInProgress,
      user: this.user
    };
  }

  // Force sync with cloud
  async forceSync() {
    if (this.isOnline) {
      return await this.syncWithCloud();
    }
    return this.loadFromLocalStorage();
  }

  // Sign out and clear user data
  async signOut() {
    if (isSupabaseConfigured()) {
      await authService.signOut();
    }
    this.user = null;
  }
}

// Create singleton instance
export const dataSyncService = new DataSyncService();

// Helper functions for backward compatibility
export async function initializeDataSync() {
  return await dataSyncService.initialize();
}

export async function updateMedicationWithSync(medications, medicationId, updates) {
  return await dataSyncService.updateMedication(medications, medicationId, updates);
}

export function getSyncStatus() {
  return dataSyncService.getSyncStatus();
}
