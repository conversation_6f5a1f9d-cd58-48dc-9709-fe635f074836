import React from 'react';
import MedicationItem from './MedicationItem';

function MedicationList({ 
  medications, 
  handleMarkTaken, 
  handleResetMedication,
  calculateNextDueTime, 
  currentTime 
}) {
  if (medications.length === 0) {
    return (
      <div className="no-medications">
        <p>No medications in this category.</p>
      </div>
    );
  }

  return (
    <div className="medication-list">
      <div className="medication-table">
        <div className="table-header">
          <div className="header-cell name">Medication</div>
          <div className="header-cell dose">Dose</div>
          <div className="header-cell frequency">Frequency</div>
          <div className="header-cell times-used">Times Used</div>
          <div className="header-cell last-taken">Last Taken</div>
          <div className="header-cell next-due">Next Due</div>
          <div className="header-cell instructions">Instructions</div>
          <div className="header-cell actions">Actions</div>
        </div>
        
        <div className="table-body">
          {medications.map((medication) => (
            <MedicationItem
              key={medication.id}
              medication={medication}
              handleMarkTaken={handleMarkTaken}
              handleResetMedication={handleResetMedication}
              calculateNextDueTime={calculateNextDueTime}
              currentTime={currentTime}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

export default MedicationList;
