// Initial medication data based on requirements
export const initialMedications = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    dose: '30 mg',
    frequency: 'Once daily',
    type: 'Required',
    instructions: 'Take with any liquid.',
    lastTaken: null,
    timesUsed: 0
  },
  {
    id: 2,
    name: 'Clindamycin',
    dose: '300 mg',
    frequency: 'Every 6 hours for 7 days',
    type: 'Required',
    instructions: 'Take 1 capsule every 6 hours.',
    lastTaken: null,
    timesUsed: 0
  },
  {
    id: 3,
    name: '<PERSON><PERSON>lex<PERSON>',
    dose: '10 mL',
    frequency: 'Every 6 hours for 7 days',
    type: 'Required',
    instructions: 'Shake well and take by mouth.',
    lastTaken: null,
    timesUsed: 0
  },
  {
    id: 4,
    name: 'Children\'s Tylenol Liquid Complete Nighttime',
    dose: 'As directed on package',
    frequency: 'As needed',
    type: 'Optional',
    instructions: 'Liquid formulation for easier swallowing. Take when needed for pain or fever.',
    lastTaken: new Date('2025-07-03T14:20:00'), // 2:20 PM today as specified
    timesUsed: 1
  },
  {
    id: 5,
    name: '<PERSON><PERSON>',
    dose: '2 tablets',
    frequency: 'Once daily when required',
    type: 'Optional',
    instructions: 'Take when constipation occurs.',
    lastTaken: null,
    timesUsed: 0
  },
  {
    id: 6,
    name: 'Morphine',
    dose: '5 mg',
    frequency: 'As needed for severe pain',
    type: 'Optional',
    instructions: 'Use cautiously; consult doctor for dosing.',
    lastTaken: null,
    timesUsed: 0
  }
];

// Helper functions for medication management
export function saveMedicationsToStorage(medications) {
  try {
    localStorage.setItem('medications', JSON.stringify(medications));
  } catch (error) {
    console.error('Failed to save medications to localStorage:', error);
  }
}

export function loadMedicationsFromStorage() {
  try {
    const savedMedications = localStorage.getItem('medications');
    if (savedMedications) {
      const parsed = JSON.parse(savedMedications);
      // Convert lastTaken strings back to Date objects and ensure timesUsed exists
      return parsed.map(med => ({
        ...med,
        lastTaken: med.lastTaken ? new Date(med.lastTaken) : null,
        timesUsed: med.timesUsed || 0
      }));
    }
  } catch (error) {
    console.error('Failed to load medications from localStorage:', error);
  }
  return initialMedications;
}

export function generateCSV(medications) {
  const headers = ['ID', 'Name', 'Dose', 'Frequency', 'Type', 'Times Used', 'Last Taken', 'Instructions'];
  const csvContent = [
    headers.join(','),
    ...medications.map(med => [
      med.id,
      `"${med.name}"`,
      `"${med.dose}"`,
      `"${med.frequency}"`,
      med.type,
      med.timesUsed || 0,
      med.lastTaken ? `"${med.lastTaken.toLocaleString()}"` : '"Not taken"',
      `"${med.instructions}"`
    ].join(','))
  ].join('\n');

  return csvContent;
}

export function downloadCSV(medications, filename = 'medication_schedule.csv') {
  const csvContent = generateCSV(medications);
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}
