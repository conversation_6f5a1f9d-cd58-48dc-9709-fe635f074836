import React from 'react';

function MedicationItem({ 
  medication, 
  handleMarkTaken, 
  handleResetMedication,
  calculateNextDueTime 
}) {
  const nextDueTime = calculateNextDueTime(medication.lastTaken, medication.frequency);
  const isOverdue = nextDueTime.includes('Overdue');
  const isRequired = medication.type === 'Required';
  
  const formatLastTaken = (lastTaken) => {
    if (!lastTaken) return 'Not taken';
    
    const now = new Date();
    const taken = new Date(lastTaken);
    const diffInHours = Math.floor((now - taken) / (1000 * 60 * 60));
    const diffInMinutes = Math.floor(((now - taken) % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffInHours < 1) {
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h ${diffInMinutes}m ago`;
    } else {
      return taken.toLocaleDateString() + ' ' + taken.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  };

  const getStatusClass = () => {
    if (!medication.lastTaken && isRequired) return 'status-not-taken-required';
    if (!medication.lastTaken) return 'status-not-taken';
    if (isOverdue && isRequired) return 'status-overdue-required';
    if (isOverdue) return 'status-overdue';
    return 'status-on-time';
  };

  return (
    <div className={`medication-row ${getStatusClass()}`}>
      <div className="cell name">
        <div className="medication-name">{medication.name}</div>
        <div className="medication-type">{medication.type}</div>
      </div>

      <div className="cell dose" data-label="Dose">
        <span className="dose-value">{medication.dose}</span>
      </div>

      <div className="cell frequency" data-label="Frequency">
        <span className="frequency-value">{medication.frequency}</span>
      </div>

      <div className="cell times-used" data-label="Times Used">
        <span className="times-used-value">
          {medication.timesUsed || 0} doses
        </span>
      </div>

      <div className="cell last-taken" data-label="Last Taken">
        <span className="last-taken-value">
          {formatLastTaken(medication.lastTaken)}
        </span>
      </div>

      <div className="cell next-due" data-label="Next Due">
        <span className={`next-due-value ${isOverdue ? 'overdue' : ''}`}>
          {nextDueTime}
        </span>
      </div>

      <div className="cell instructions" data-label="Instructions">
        <div className="instructions-text">
          {medication.instructions}
        </div>
      </div>

      <div className="cell actions" data-label="Actions">
        <div className="action-buttons">
          <button
            onClick={() => handleMarkTaken(medication.id)}
            className="mark-btn"
            title="Mark as taken now"
          >
            ✅ Take Now
          </button>
          {medication.lastTaken && (
            <button
              onClick={() => handleResetMedication(medication.id)}
              className="reset-btn"
              title="Reset to not taken"
            >
              🔄 Reset
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

export default MedicationItem;
